import winston from "winston";
import { format } from "date-fns";
import { CronExpressionParser } from "cron-parser";
import * as cron from "node-cron";
import {
  loadJobDefinitions,
  loadJobDefinition,
  updateJobEnabled as persistUpdateJobEnabled,
  saveJobExecution,
  loadJobExecutions,
  getLatestJobExecution,
  addJobExecutionLog,
  deleteJobDefinition,
} from "./jobPersistence";
import { initializeDatabase } from "./database";

// Create a Winston logger instance
const logger = winston.createLogger({
  level: "info",
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: { service: "data-bot" },
  transports: [
    // Write to error.log for errors
    new winston.transports.File({
      filename: "logs/error.log",
      level: "error",
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    }),
    // Write to combined.log for all logs
    new winston.transports.File({
      filename: "logs/combined.log",
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    }),
  ],
});

// Add console transport in development
if (process.env.NODE_ENV !== "production") {
  logger.add(
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      ),
    })
  );
}

// Database operation types
export type DatabaseOperationType =
  | "CREATE_TABLE_AS_SELECT"
  | "DROP_TABLE"
  | "ALTER_TABLE"
  | "MULTI_STEP_DDL"
  | "DATA_EXTRACTION";

// Join types for visual query builder
export type JoinType = "INNER" | "LEFT" | "RIGHT" | "FULL";

// Aggregation functions
export type AggregationFunction =
  | "SUM"
  | "COUNT"
  | "AVG"
  | "MIN"
  | "MAX"
  | "GROUP_CONCAT";

// Table relationship definition
export interface TableRelationship {
  id: string;
  sourceTable: string;
  sourceColumn: string;
  targetTable: string;
  targetColumn: string;
  joinType: JoinType;
}

// Column selection with aggregation
export interface ColumnSelection {
  id: string;
  table: string;
  column: string;
  alias?: string;
  aggregation?: AggregationFunction;
  expression?: string; // For custom expressions like ROUND(SUM(col), 0)
}

// WHERE condition
export interface WhereCondition {
  id: string;
  column: string;
  operator: "=" | "!=" | ">" | "<" | ">=" | "<=" | "LIKE" | "IN" | "BETWEEN";
  value: string | string[];
  logicalOperator?: "AND" | "OR";
}

// Visual query builder configuration
export interface VisualQueryConfig {
  sourceTables: string[];
  relationships: TableRelationship[];
  selectedColumns: ColumnSelection[];
  whereConditions: WhereCondition[];
  groupByColumns: string[];
  orderByColumns: { column: string; direction: "ASC" | "DESC" }[];
  targetTable?: string;
  operationType: DatabaseOperationType;
}

// Operation modes for database administration
export type DatabaseOperationMode =
  | "table_management" // DDL operations (CREATE/DROP/ALTER) - no data export
  | "data_extraction" // SELECT queries - export to destinations
  | "workflow"; // Multi-step operations - mixed behavior

// Database administration specific configuration
export interface DatabaseAdminConfig {
  host: string;
  port: number;
  database: string;
  username: string;
  password: string;
  connectionLimit?: number;
  acquireTimeout?: number;
  timeout?: number;
  // Operation mode determines behavior and destination requirements
  operationMode: DatabaseOperationMode;
  // Multi-step operations
  operations: DatabaseOperation[];
  // Visual query builder config
  visualQuery?: VisualQueryConfig;
  // Raw SQL fallback
  rawSql?: string;
}

// Individual database operation
export interface DatabaseOperation {
  id: string;
  type: DatabaseOperationType;
  name: string;
  description?: string;
  sql: string;
  dependsOn?: string[]; // IDs of operations that must complete first
  continueOnError?: boolean;
}

export interface JobDefinition {
  id: string;
  name: string;
  description: string;
  schedule: string; // Cron expression
  enabled: boolean;
  dataSource: {
    type:
      | "oracle"
      | "sftp"
      | "mysql"
      | "database_admin"
      | "pdf_dipa"
      | "adk_processing";
    // Oracle Database configuration
    oracle?: {
      host: string;
      port: number;
      serviceName: string; // or SID
      username: string;
      password: string;
      query: string; // SQL query to execute - use ROWNUM, LIMIT, etc. for row limits
      schema?: string;
      batchSize?: number; // Batch size for streaming (default: 10000)
      streamProcessing?: boolean; // Enable streaming for large datasets (default: true)
      maxRows?: number; // Optional row limit (only applied if explicitly set)
    };
    // MySQL Database configuration
    mysql?: {
      host: string;
      port: number;
      database: string;
      username: string;
      password: string;
      query: string; // SQL query to execute
      connectionLimit?: number; // Optional connection pool limit
      acquireTimeout?: number; // Optional connection timeout
      timeout?: number; // Optional query timeout
    };
    // Database Administration configuration
    database_admin?: DatabaseAdminConfig;
    // SFTP configuration
    sftp?: {
      host: string;
      port: number;
      username: string;
      password?: string;
      privateKey?: string; // SSH private key path or content
      remotePath: string; // Path to file/directory on SFTP server
      filePattern?: string; // File pattern to match (e.g., "*.csv", "data_*.txt")
    };
    // PDF DIPA processing configuration
    pdf_dipa?: {
      sourceDirectory: string; // Directory containing PDF files to process
      fileMetadataDatabase: {
        host: string;
        port: number;
        database: string;
        username: string;
        password: string;
        table: string; // Table that tracks file metadata (e.g., file_metadata)
      };
      fileStatusFilter?: string; // Status to filter files by (defaults to 'NEW' for incremental processing)
      errorLogTable?: string; // Table for error logging (optional, defaults to log_ftp.error_logs)
    };
    // ADK Processing configuration
    adk_processing?: {
      sourceDirectory: string; // Base directory containing compressed ADK files (e.g., C:\KUMPULAN_ADK\ADK_2024_DIPA)
      extractionPath: string; // Temporary directory for XML extraction (e.g., C:\KUMPULAN_ADK\XML)
      rarToolPath: string; // Path to RAR extraction tool (e.g., C:\KUMPULAN_ADK\TOOLS\Rar.exe)
      fileListDatabase: {
        host: string;
        port: number;
        database: string;
        username: string;
        password: string;
        table: string; // Table containing file list (e.g., monev2024.ftp_baru_2024)
      };
      fileFilter: {
        startsWith: string[]; // File name prefixes to process (e.g., ['d', 'D'])
        excludeExtensions: string[]; // File extensions to exclude (e.g., ['pdf'])
      };
      processingOptions?: {
        deleteOldXmlFiles?: boolean; // Whether to clean up old XML files before extraction
        continueOnError?: boolean; // Whether to continue processing other files if one fails
        batchSize?: number; // Number of files to process in each batch
      };
    };
    options?: Record<string, unknown>;
  };
  destination: {
    type: "database" | "file" | "local";
    // Local file system destination
    localPath?: string;
    // Database destination (Oracle or other)
    database?: {
      type: "oracle" | "postgres" | "mysql";
      host: string;
      port: number;
      database: string;
      username: string;
      password: string;
      table: string;
      schema?: string;
    };
    options?: Record<string, unknown>;
    // File tracking configuration (for SFTP jobs with local destination)
    fileTracking?: {
      enabled: boolean;
      database?: {
        host: string;
        port: number;
        username: string;
        password: string;
        database: string;
        table: string;
      };
    };
  };
  retryConfig: {
    maxRetries: number;
    retryDelay: number; // in seconds
  };
  sequenceConfig?: {
    sequenceId: string;
    order: number;
  };
}

export interface JobExecution {
  id: string;
  jobId: string;
  status: "running" | "completed" | "failed" | "scheduled" | "stopped";
  triggerType: "manual" | "automatic";
  startTime: Date;
  endTime?: Date;
  duration?: number; // in seconds
  recordsProcessed?: number;
  errorMessage?: string;
  logs: string[];
}

// Job sequence failure handling strategies
export type SequenceFailureAction = "stop" | "continue" | "retry";

// Job sequence definition
export interface JobSequence {
  id: string;
  name: string;
  description: string;
  schedule?: string; // Cron expression for sequence start (optional)
  enabled: boolean;
  onFailure: SequenceFailureAction;
  maxRetries: number;
  jobs: string[]; // Ordered list of job IDs in the sequence
  createdAt?: Date;
  updatedAt?: Date;
}

// Job sequence execution tracking
export interface JobSequenceExecution {
  id: string;
  sequenceId: string;
  status: "running" | "completed" | "failed" | "stopped";
  currentJobId?: string;
  currentJobOrder?: number;
  startTime: Date;
  endTime?: Date;
  duration?: number; // in seconds
  triggerType: "manual" | "automatic";
  errorMessage?: string;
}

// Sequence execution state for in-memory tracking
export interface SequenceExecutionState {
  execution: JobSequenceExecution;
  sequence: JobSequence;
  currentJobIndex: number;
  retryCount: number;
}

// In-memory storage for backward compatibility and caching
// The primary storage is now in the database
let jobDefinitionsCache: JobDefinition[] = [];
let jobExecutionsCache: JobExecution[] = [];

// Initialize the persistent storage
export async function initializeJobManager(): Promise<void> {
  try {
    logger.info("Initializing job manager with persistent storage...");

    // Initialize database
    await initializeDatabase();

    // Load existing job definitions from database
    jobDefinitionsCache = await loadJobDefinitions();

    // Note: Default jobs are now created by the migration script (migrate.mjs)
    // If no jobs exist, they should be created by running the migration

    // Load recent job executions
    jobExecutionsCache = await loadJobExecutions(undefined, 100);

    logger.info(
      `Job manager initialized with ${jobDefinitionsCache.length} jobs and ${jobExecutionsCache.length} recent executions`
    );
  } catch (error) {
    logger.error("Failed to initialize job manager:", error);
    throw error;
  }
}

export async function getJobDefinitions(): Promise<JobDefinition[]> {
  try {
    // Refresh cache from database
    jobDefinitionsCache = await loadJobDefinitions();
    return jobDefinitionsCache;
  } catch (error) {
    logger.error("Failed to get job definitions, using cache:", error);
    return jobDefinitionsCache;
  }
}

export async function getJobDefinition(
  jobId: string
): Promise<JobDefinition | null> {
  try {
    // Try to get from database first
    const job = await loadJobDefinition(jobId);
    if (job) return job;

    // Fallback to cache
    return jobDefinitionsCache.find((job) => job.id === jobId) || null;
  } catch (error) {
    logger.error(`Failed to get job definition ${jobId}, using cache:`, error);
    return jobDefinitionsCache.find((job) => job.id === jobId) || null;
  }
}

export async function updateJobEnabled(
  jobId: string,
  enabled: boolean
): Promise<void> {
  try {
    // Update in database
    await persistUpdateJobEnabled(jobId, enabled);

    // Update cache
    const job = jobDefinitionsCache.find((job) => job.id === jobId);
    if (job) {
      job.enabled = enabled;
    }

    logger.info(`Job ${jobId} enabled state updated to ${enabled}`, {
      jobId,
      enabled,
    });

    // Broadcast the update to all connected clients
    try {
      const { broadcastJobUpdate } = await import("@/lib/sseManager");
      await broadcastJobUpdate();
    } catch (error) {
      logger.debug("Could not broadcast job update", { error });
    }
  } catch (error) {
    logger.error(`Failed to update job enabled state ${jobId}:`, error);
    throw error;
  }
}

export async function getJobStatus() {
  try {
    // Get the latest job definitions
    const currentJobs = await getJobDefinitions();

    // Get the latest execution for each job from database
    const latestExecutions = new Map<string, JobExecution>();

    // Load recent executions for all jobs
    for (const job of currentJobs) {
      const latestExecution = await getLatestJobExecution(job.id);
      if (latestExecution) {
        latestExecutions.set(job.id, latestExecution);
      }
    }

    // Load sequence information for jobs that are part of sequences
    const sequenceMap = new Map<
      string,
      { id: string; name: string; enabled: boolean }
    >();
    const sequenceIds = new Set(
      currentJobs
        .filter((job) => job.sequenceConfig)
        .map((job) => job.sequenceConfig!.sequenceId)
    );

    // Import sequence loading function
    const { loadJobSequence } = await import("./sequencePersistence");

    for (const sequenceId of sequenceIds) {
      try {
        const sequence = await loadJobSequence(sequenceId);
        if (sequence) {
          sequenceMap.set(sequenceId, {
            id: sequence.id,
            name: sequence.name,
            enabled: sequence.enabled,
          });
        }
      } catch (error) {
        logger.warn(`Failed to load sequence ${sequenceId}:`, error);
      }
    }

    return currentJobs.map((job) => {
      const latestExecution = latestExecutions.get(job.id);
      const nextRun = getNextRunTime(job.schedule);

      // Determine display status and last run result
      let displayStatus: "running" | "scheduled" | "stopped" = "scheduled";
      let lastRunStatus: "completed" | "failed" | "cancelled" | undefined =
        undefined;

      if (latestExecution) {
        if (latestExecution.status === "running") {
          displayStatus = "running";
        } else if (latestExecution.status === "stopped") {
          // Show stopped status immediately after cancellation, regardless of enabled state
          displayStatus = "stopped";
          lastRunStatus = "cancelled";
        } else if (job.enabled) {
          displayStatus = "scheduled";
          // Set last run status based on the previous execution result
          if (
            latestExecution.status === "completed" ||
            latestExecution.status === "failed"
          ) {
            lastRunStatus = latestExecution.status;
          }
        } else {
          displayStatus = "stopped";
        }
      } else if (!job.enabled) {
        displayStatus = "stopped";
      }

      // Get parent sequence information if job is part of a sequence
      const parentSequence = job.sequenceConfig
        ? sequenceMap.get(job.sequenceConfig.sequenceId)
        : undefined;

      return {
        id: job.id,
        name: job.name,
        status: displayStatus,
        lastRun:
          latestExecution?.startTime ||
          new Date(Date.now() - 24 * 60 * 60 * 1000),
        nextRun,
        duration: latestExecution?.duration || 0,
        logs: latestExecution?.logs || ["Waiting for first execution..."],
        enabled: job.enabled,
        lastRunStatus,
        lastRunTriggerType: latestExecution?.triggerType,
        sequenceConfig: job.sequenceConfig,
        parentSequence,
      };
    });
  } catch (error) {
    logger.error("Failed to get job status:", error);
    // Return empty array or cached data as fallback
    return [];
  }
}

export async function updateJobStatus(
  jobId: string,
  status: JobExecution["status"],
  details?: Partial<JobExecution>
) {
  try {
    // Find the execution in cache - look for running first, then any recent execution
    let existingExecution = jobExecutionsCache.find(
      (exec) => exec.jobId === jobId && exec.status === "running"
    );

    // If no running execution found, look for the most recent execution for this job
    if (!existingExecution) {
      const jobExecutions = jobExecutionsCache
        .filter((exec) => exec.jobId === jobId)
        .sort((a, b) => b.startTime.getTime() - a.startTime.getTime());
      existingExecution = jobExecutions[0];
    }

    if (existingExecution) {
      existingExecution.status = status;
      if (details) {
        Object.assign(existingExecution, details);
      }
      if (
        status === "completed" ||
        status === "failed" ||
        status === "stopped"
      ) {
        existingExecution.endTime = new Date();
        existingExecution.duration = Math.round(
          (existingExecution.endTime.getTime() -
            existingExecution.startTime.getTime()) /
            1000
        );
      }

      // Save to database
      await saveJobExecution(existingExecution);

      logger.info(`Job ${jobId} status updated to ${status}`, {
        jobId,
        status,
        executionId: existingExecution.id,
      });

      // Broadcast the update to all connected clients
      try {
        const { broadcastJobUpdate } = await import("@/lib/sseManager");
        await broadcastJobUpdate();
      } catch (error) {
        // SSE module might not be available during startup
        logger.debug("Could not broadcast job update", { error });
      }
    } else {
      // No existing execution found, create a new one for the cancellation
      logger.warn(
        `No existing execution found for job ${jobId}, creating new execution for status update`
      );
      const executionId = `${jobId}-${Date.now()}`;
      const newExecution: JobExecution = {
        id: executionId,
        jobId,
        status,
        triggerType: "manual",
        startTime: new Date(),
        endTime: new Date(),
        duration: 0,
        logs: details?.logs || ["Job cancelled"],
        errorMessage: details?.errorMessage,
      };

      // Add to cache
      jobExecutionsCache.push(newExecution);

      // Save to database
      await saveJobExecution(newExecution);

      logger.info(
        `Created new execution for job ${jobId} with status ${status}`,
        {
          jobId,
          status,
          executionId,
        }
      );

      // Broadcast the update to all connected clients
      try {
        const { broadcastJobUpdate } = await import("@/lib/sseManager");
        await broadcastJobUpdate();
      } catch (error) {
        logger.debug("Could not broadcast job update", { error });
      }
    }
  } catch (error) {
    logger.error(`Failed to update job status ${jobId}:`, error);
    throw error;
  }
}

export async function createJobExecution(
  jobId: string,
  triggerType: "manual" | "automatic" = "automatic"
): Promise<string> {
  try {
    const executionId = `${jobId}-${Date.now()}`;
    const execution: JobExecution = {
      id: executionId,
      jobId,
      status: "running",
      triggerType,
      startTime: new Date(),
      logs: ["Job execution started..."],
    };

    // Add to cache
    jobExecutionsCache.push(execution);

    // Save to database
    await saveJobExecution(execution);

    // Add initial log to database
    await addJobExecutionLog(executionId, "Job execution started...", "info");

    logger.info(`Created job execution for job ${jobId}`, {
      jobId,
      executionId,
    });

    // Broadcast the update to all connected clients
    try {
      const { broadcastJobUpdate } = await import("@/lib/sseManager");
      await broadcastJobUpdate();
    } catch (error) {
      // SSE module might not be available during startup
      logger.debug("Could not broadcast job update", { error });
    }

    return executionId;
  } catch (error) {
    logger.error(`Failed to create job execution for ${jobId}:`, error);
    throw error;
  }
}

export async function addJobLog(jobId: string, message: string) {
  try {
    const execution = jobExecutionsCache.find(
      (exec) => exec.jobId === jobId && exec.status === "running"
    );

    if (execution) {
      const logMessage = `${format(new Date(), "HH:mm:ss")} - ${message}`;
      execution.logs.push(logMessage);

      // Implement log rotation to prevent memory bloat
      // Keep only the last 1000 logs in memory for performance
      const MAX_LOGS_IN_MEMORY = 1000;
      if (execution.logs.length > MAX_LOGS_IN_MEMORY) {
        execution.logs = execution.logs.slice(-MAX_LOGS_IN_MEMORY);
      }

      // Add log to database
      await addJobExecutionLog(execution.id, message, "info");

      logger.info(`Job ${jobId} log: ${message}`, { jobId });

      // Broadcast the update (now debounced) when logs are added
      try {
        const { broadcastJobUpdate } = await import("@/lib/sseManager");
        await broadcastJobUpdate();
      } catch (error) {
        logger.debug("Could not broadcast job update", { error });
      }
    }
  } catch (error) {
    logger.error(`Failed to add job log for ${jobId}:`, error);
    throw error;
  }
}

// Helper function to calculate next run time based on cron expression
function getNextRunTime(cronExpression: string): Date {
  try {
    // Use cron-parser for accurate next run calculation
    // Parse the cron expression and get the next occurrence
    const interval = CronExpressionParser.parse(cronExpression, {
      currentDate: new Date(),
      tz: process.env.TIMEZONE || "Asia/Jakarta",
    });

    const nextRun = interval.next().toDate();

    logger.debug(
      `Next run calculated for cron "${cronExpression}": ${nextRun.toISOString()}`
    );
    return nextRun;
  } catch (error) {
    logger.warn(
      `Error parsing cron expression "${cronExpression}": ${
        error instanceof Error ? error.message : "Unknown error"
      }`
    );

    // Fallback to node-cron validation and simple calculation
    try {
      if (!cron.validate(cronExpression)) {
        logger.warn(
          `Invalid cron expression: ${cronExpression}, using default next run time`
        );
        // Default: next day at 2 AM
        const nextRun = new Date();
        nextRun.setDate(nextRun.getDate() + 1);
        nextRun.setHours(2, 0, 0, 0);
        return nextRun;
      }

      // Simple fallback calculation for common patterns
      const now = new Date();
      const [minute, hour, dayOfMonth, month, dayOfWeek] =
        cronExpression.split(" ");

      // Handle special cases first
      if (cronExpression === "* * * * *") {
        return new Date(now.getTime() + 60 * 1000); // Next minute
      }

      // For daily jobs (e.g., "0 2 * * *")
      if (
        minute !== "*" &&
        hour !== "*" &&
        dayOfMonth === "*" &&
        month === "*" &&
        dayOfWeek === "*"
      ) {
        const targetMinute = parseInt(minute);
        const targetHour = parseInt(hour);

        if (!isNaN(targetMinute) && !isNaN(targetHour)) {
          const nextRun = new Date();
          nextRun.setHours(targetHour, targetMinute, 0, 0);

          // If the time has already passed today, schedule for tomorrow
          if (nextRun <= now) {
            nextRun.setDate(nextRun.getDate() + 1);
          }

          return nextRun;
        }
      }

      // Default: next day at 2 AM
      const nextRun = new Date();
      nextRun.setDate(nextRun.getDate() + 1);
      nextRun.setHours(2, 0, 0, 0);
      return nextRun;
    } catch (fallbackError) {
      logger.error(
        `Fallback cron calculation also failed for "${cronExpression}": ${
          fallbackError instanceof Error
            ? fallbackError.message
            : "Unknown error"
        }`
      );
      // Ultimate fallback: next day at 2 AM
      const nextRun = new Date();
      nextRun.setDate(nextRun.getDate() + 1);
      nextRun.setHours(2, 0, 0, 0);
      return nextRun;
    }
  }
}

// Function to delete specific jobs from database
export async function deleteJobsFromDatabase(jobIds: string[]): Promise<void> {
  try {
    logger.info(`Starting deletion of jobs: ${jobIds.join(", ")}`);

    for (const jobId of jobIds) {
      try {
        // Check if job exists first
        const existingJob = await loadJobDefinition(jobId);
        if (existingJob) {
          await deleteJobDefinition(jobId);
          logger.info(`Successfully deleted job ${jobId}: ${existingJob.name}`);

          // Remove from cache if it exists
          const cacheIndex = jobDefinitionsCache.findIndex(
            (job) => job.id === jobId
          );
          if (cacheIndex !== -1) {
            jobDefinitionsCache.splice(cacheIndex, 1);
          }
        } else {
          logger.warn(`Job ${jobId} not found in database, skipping`);
        }
      } catch (error) {
        logger.error(`Failed to delete job ${jobId}:`, error);
        throw error;
      }
    }

    logger.info(`Completed deletion of ${jobIds.length} jobs`);

    // Broadcast the update to all connected clients
    try {
      const { broadcastJobUpdate } = await import("@/lib/sseManager");
      await broadcastJobUpdate();
    } catch (error) {
      logger.debug("Could not broadcast job update", { error });
    }
  } catch (error) {
    logger.error("Failed to delete jobs from database:", error);
    throw error;
  }
}

// Function to specifically delete jobs 2 and 3
export async function deleteOldJobs(): Promise<void> {
  await deleteJobsFromDatabase(["2", "3"]);
}

export { logger };
