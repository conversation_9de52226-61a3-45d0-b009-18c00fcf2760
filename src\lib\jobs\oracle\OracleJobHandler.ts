import { BaseJobHandler } from "../base/BaseJobHandler";
import type { <PERSON><PERSON><PERSON><PERSON>, JobExecutionContext } from "../types";
import type { JobDefinition } from "../../jobManager";
import {
  executeOracleQuery,
  executeOracleQueryStream,
} from "../../../tarikData/db/Oracle.js";
import { logger } from "../../jobManager";

// Type for Oracle query result rows
type OracleRow = Record<string, unknown>;

// Type for Oracle query result
interface OracleQueryResult {
  rows?: OracleRow[];
  metaData?: Array<{ name: string }>;
}

/**
 * Job handler for Oracle database operations
 */
export class OracleJobHandler extends BaseJobHandler {
  public readonly jobType = "oracle";

  /**
   * Execute Oracle database job
   */
  public async execute(context: JobExecutionContext): Promise<JobResult> {
    const { jobDefinition } = context;
    const oracleConfig = jobDefinition.dataSource.oracle;

    if (!oracleConfig) {
      throw new Error(
        "Oracle configuration is required for Oracle data source"
      );
    }

    this.validateOracleConfig(oracleConfig);

    const startTime = await this.logOperationStart(
      context,
      "Oracle database connection",
      `${oracleConfig.host}:${oracleConfig.port}/${oracleConfig.serviceName}`
    );

    try {
      await this.simulateNetworkDelay(1000);
      await context.checkCancellation();

      await context.addLog("Connected to Oracle database successfully");

      // Choose streaming or regular processing based on configuration
      // Default to streaming mode for better performance with large datasets
      if (oracleConfig.streamProcessing === false) {
        return await this.executeRegularQuery(context, oracleConfig, startTime);
      } else {
        return await this.executeStreamingQuery(
          context,
          oracleConfig,
          startTime
        );
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown Oracle error";
      await context.addLog(`Oracle operation failed: ${errorMessage}`);

      logger.error(
        `Oracle database operation failed for job ${context.jobId}`,
        {
          jobId: context.jobId,
          error: errorMessage,
          host: oracleConfig.host,
          port: oracleConfig.port,
          serviceName: oracleConfig.serviceName,
        }
      );

      throw new Error(`Oracle database operation failed: ${errorMessage}`);
    }
  }

  private async executeStreamingQuery(
    context: JobExecutionContext,
    oracleConfig: NonNullable<JobDefinition["dataSource"]["oracle"]>,
    startTime: number
  ): Promise<JobResult> {
    const batchSize = oracleConfig.batchSize || 10000;
    let totalRecords = 0;
    let batchCount = 0;

    await context.addLog(
      `🚀 Starting high-performance streaming with batch size: ${batchSize}`
    );

    await context.addLog(
      "✅ Streaming mode: Processing data in batches and writing directly to destination"
    );

    // Get destination writer for incremental writing
    const destinationWriter = await this.createDestinationWriter(context);

    await context.executeWithCancellationCheck(
      () =>
        executeOracleQueryStream(
          oracleConfig.query,
          [],
          {
            user: oracleConfig.username,
            password: oracleConfig.password,
            connectString: `${oracleConfig.host}:${oracleConfig.port}/${oracleConfig.serviceName}`,
            batchSize,
          },
          async (batch: OracleRow[]) => {
            await context.checkCancellation();

            batchCount++;
            totalRecords += batch.length;

            // Write batch directly to destination
            await destinationWriter.writeBatch(batch);

            await context.addLog(
              `✅ Batch ${batchCount}: ${batch.length} records processed and saved (Total: ${totalRecords})`
            );

            // Progress updates for large datasets
            if (totalRecords % 50000 === 0) {
              await context.addLog(
                `📊 Progress milestone: ${totalRecords} records processed and saved to destination`
              );
            }
          }
        ),
      "Oracle streaming query execution"
    );

    // Finalize destination writing
    await destinationWriter.finalize();

    await context.addLog(
      `🎉 Streaming completed! ${totalRecords} records successfully saved to destination`
    );

    await this.logOperationComplete(
      context,
      "Oracle streaming operation",
      startTime,
      `${totalRecords} records processed and saved in ${batchCount} batches`
    );

    logger.info(
      `Oracle streaming query executed successfully for job ${context.jobId}`,
      {
        jobId: context.jobId,
        totalRecords,
        batchCount,
        batchSize,
        query: oracleConfig.query.substring(0, 100) + "...",
      }
    );

    // Return result with total count (no data array to save memory)
    return this.createJobResult(totalRecords, []);
  }

  private async executeRegularQuery(
    context: JobExecutionContext,
    oracleConfig: NonNullable<JobDefinition["dataSource"]["oracle"]>,
    startTime: number
  ): Promise<JobResult> {
    await context.addLog("Executing SQL query...");

    const result = (await context.executeWithCancellationCheck(
      () =>
        executeOracleQuery(oracleConfig.query, [], {
          user: oracleConfig.username,
          password: oracleConfig.password,
          connectString: `${oracleConfig.host}:${oracleConfig.port}/${oracleConfig.serviceName}`,
          // Only apply maxRows if explicitly set by user, no default limit
          ...(oracleConfig.maxRows && { maxRows: oracleConfig.maxRows }),
        }),
      "Oracle query execution"
    )) as OracleQueryResult;

    const recordCount = result.rows ? result.rows.length : 0;

    await context.addLog(
      `Query executed successfully. Retrieved ${recordCount} records`
    );

    await this.logOperationComplete(
      context,
      "Oracle database operation",
      startTime,
      `${recordCount} records retrieved`
    );

    logger.info(`Oracle query executed successfully for job ${context.jobId}`, {
      jobId: context.jobId,
      recordCount,
      query: oracleConfig.query.substring(0, 100) + "...",
    });

    return this.createJobResult(recordCount, result.rows || []);
  }

  /**
   * Create a destination writer for streaming data directly to destination
   */
  private async createDestinationWriter(context: JobExecutionContext) {
    const jobDef = context.jobDefinition;

    if (
      jobDef.destination.type === "database" &&
      jobDef.destination.database?.type === "mysql"
    ) {
      return new MySQLDestinationWriter(jobDef, context);
    }

    // For other destinations, use a no-op writer for now
    return new NoOpDestinationWriter(context);
  }

  /**
   * Validate Oracle job configuration
   */
  public validateConfig(jobDef: JobDefinition): boolean {
    if (!super.validateConfig(jobDef)) {
      return false;
    }

    const oracleConfig = jobDef.dataSource.oracle;
    if (!oracleConfig) {
      return false;
    }

    try {
      this.validateOracleConfig(oracleConfig);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get required permissions for Oracle operations
   */
  public getRequiredPermissions(): string[] {
    return ["database:oracle:read"];
  }

  /**
   * Validate Oracle configuration fields
   */
  private validateOracleConfig(
    config: NonNullable<JobDefinition["dataSource"]["oracle"]>
  ): void {
    this.validateRequiredFields(
      config,
      ["host", "port", "serviceName", "username", "password", "query"],
      "Oracle configuration"
    );

    if (
      typeof config.port !== "number" ||
      config.port <= 0 ||
      config.port > 65535
    ) {
      throw new Error("Oracle port must be a valid number between 1 and 65535");
    }

    if (!config.query.trim()) {
      throw new Error("Oracle query cannot be empty");
    }

    // Validate batch size (streaming is default unless explicitly disabled)
    if (config.streamProcessing !== false && config.batchSize) {
      if (config.batchSize <= 0 || config.batchSize > 100000) {
        throw new Error("Batch size must be between 1 and 100000");
      }
    }

    // Basic SQL injection prevention
    const dangerousPatterns = [
      /;\s*drop\s+/i,
      /;\s*delete\s+/i,
      /;\s*truncate\s+/i,
      /;\s*alter\s+/i,
      /;\s*create\s+/i,
    ];

    for (const pattern of dangerousPatterns) {
      if (pattern.test(config.query)) {
        throw new Error(
          "Oracle query contains potentially dangerous SQL statements"
        );
      }
    }
  }
}

/**
 * MySQL destination writer for streaming Oracle data
 */
class MySQLDestinationWriter {
  private connection: any;
  private tableName: string;
  private columns: string[] | null = null;
  private insertSQL: string = "";
  private totalInserted = 0;

  constructor(
    private jobDef: JobDefinition,
    private context: JobExecutionContext
  ) {
    this.tableName = jobDef.destination.database!.table;
  }

  async writeBatch(batch: OracleRow[]): Promise<void> {
    if (batch.length === 0) return;

    // Initialize connection and table structure on first batch
    if (!this.connection) {
      await this.initialize(batch[0]);
    }

    // Use batch insertion for performance
    await this.insertBatchData(batch);
  }

  private async initialize(firstRow: OracleRow): Promise<void> {
    const mysql2 = await import("mysql2/promise");
    const dbConfig = this.jobDef.destination.database!;

    // Create MySQL connection
    this.connection = await mysql2.createConnection({
      host: dbConfig.host,
      port: dbConfig.port,
      user: dbConfig.username,
      password: dbConfig.password,
      database: dbConfig.database,
    });

    await this.context.addLog("Connected to destination MySQL database");

    // Get column structure from first row
    this.columns = Object.keys(firstRow);

    // Create table if it doesn't exist
    await this.createTableIfNotExists();

    // Prepare INSERT statement
    const placeholders = this.columns.map(() => "?").join(", ");
    this.insertSQL = `INSERT INTO \`${this.tableName}\` (\`${this.columns.join(
      "`, `"
    )}\`) VALUES (${placeholders})`;
  }

  private async createTableIfNotExists(): Promise<void> {
    try {
      // Check if table exists using a simpler query
      const [tables] = await this.connection.execute(
        `SHOW TABLES LIKE '${this.tableName}'`
      );

      if ((tables as any[]).length === 0) {
        await this.context.addLog(`Creating table ${this.tableName}...`);

        // Create table with generic VARCHAR columns (can be optimized later)
        const columnDefs = this.columns!.map((col) => `\`${col}\` TEXT`).join(
          ", "
        );
        const createSQL = `CREATE TABLE IF NOT EXISTS \`${this.tableName}\` (${columnDefs})`;

        await this.connection.execute(createSQL);
        await this.context.addLog(
          `Table ${this.tableName} created successfully`
        );
      } else {
        await this.context.addLog(`Table ${this.tableName} already exists`);
      }
    } catch (error) {
      await this.context.addLog(`Error checking/creating table: ${error}`);
      // Try to create table anyway with fallback method
      try {
        const columnDefs = this.columns!.map((col) => `\`${col}\` TEXT`).join(
          ", "
        );
        const createSQL = `CREATE TABLE IF NOT EXISTS \`${this.tableName}\` (${columnDefs})`;
        await this.connection.execute(createSQL);
        await this.context.addLog(
          `Table ${this.tableName} created with fallback method`
        );
      } catch (fallbackError) {
        await this.context.addLog(`Failed to create table: ${fallbackError}`);
        throw fallbackError;
      }
    }
  }

  private async insertBatchData(batch: OracleRow[]): Promise<void> {
    const batchSize = 1000; // Insert 1000 records per SQL statement

    for (let i = 0; i < batch.length; i += batchSize) {
      const chunk = batch.slice(i, i + batchSize);
      await this.insertChunk(chunk);
    }
  }

  private async insertChunk(chunk: OracleRow[]): Promise<void> {
    if (chunk.length === 1) {
      // Single row insert
      const values = this.columns!.map((col) =>
        this.processValue(chunk[0][col])
      );
      await this.connection.execute(this.insertSQL, values);
      this.totalInserted++;
    } else {
      // Batch insert
      const batchValues: unknown[] = [];
      const valuePlaceholders: string[] = [];
      const placeholders = this.columns!.map(() => "?").join(", ");

      for (const row of chunk) {
        const processedValues = this.columns!.map((col) =>
          this.processValue(row[col])
        );
        batchValues.push(...processedValues);
        valuePlaceholders.push(`(${placeholders})`);
      }

      const batchInsertSQL = `INSERT INTO \`${
        this.tableName
      }\` (\`${this.columns!.join("`, `")}\`) VALUES ${valuePlaceholders.join(
        ", "
      )}`;
      await this.connection.execute(batchInsertSQL, batchValues);
      this.totalInserted += chunk.length;
    }
  }

  private processValue(value: unknown): unknown {
    if (value === null || value === undefined) return null;
    if (value instanceof Date) {
      return value.toISOString().slice(0, 19).replace("T", " ");
    }
    if (
      typeof value === "string" &&
      /^(Mon|Tue|Wed|Thu|Fri|Sat|Sun)/.test(value)
    ) {
      try {
        const dateObj = new Date(value);
        if (!isNaN(dateObj.getTime())) {
          return dateObj.toISOString().slice(0, 19).replace("T", " ");
        }
      } catch (error) {
        console.warn(`Failed to parse date string: ${value}`);
      }
    }
    return value;
  }

  async finalize(): Promise<void> {
    if (this.connection) {
      await this.connection.end();
      await this.context.addLog(
        `Total records inserted: ${this.totalInserted}`
      );
    }
  }
}

/**
 * No-op destination writer for unsupported destinations
 */
class NoOpDestinationWriter {
  constructor(private context: JobExecutionContext) {}

  async writeBatch(batch: OracleRow[]): Promise<void> {
    await this.context.addLog(
      `Processed batch of ${batch.length} records (destination not supported for streaming)`
    );
  }

  async finalize(): Promise<void> {
    await this.context.addLog(
      "Streaming completed (no destination writing performed)"
    );
  }
}
