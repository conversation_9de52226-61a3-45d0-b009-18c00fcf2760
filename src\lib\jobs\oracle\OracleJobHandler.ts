import { BaseJobHandler } from "../base/BaseJobHandler";
import type { <PERSON><PERSON><PERSON><PERSON>, JobExecutionContext } from "../types";
import type { JobDefinition } from "../../jobManager";
import {
  executeOracleQuery,
  executeOracleQueryStream,
} from "../../../tarikData/db/Oracle.js";
import { logger } from "../../jobManager";

// Type for Oracle query result rows
type OracleRow = Record<string, unknown>;

// Type for Oracle query result
interface OracleQueryResult {
  rows?: OracleRow[];
  metaData?: Array<{ name: string }>;
}

/**
 * Job handler for Oracle database operations
 */
export class OracleJobHandler extends BaseJobHandler {
  public readonly jobType = "oracle";

  /**
   * Execute Oracle database job
   */
  public async execute(context: JobExecutionContext): Promise<JobResult> {
    const { jobDefinition } = context;
    const oracleConfig = jobDefinition.dataSource.oracle;

    if (!oracleConfig) {
      throw new Error(
        "Oracle configuration is required for Oracle data source"
      );
    }

    this.validateOracleConfig(oracleConfig);

    const startTime = await this.logOperationStart(
      context,
      "Oracle database connection",
      `${oracleConfig.host}:${oracleConfig.port}/${oracleConfig.serviceName}`
    );

    try {
      await this.simulateNetworkDelay(1000);
      await context.checkCancellation();

      await context.addLog("Connected to Oracle database successfully");

      // Choose streaming or regular processing based on configuration
      // Default to streaming mode unless explicitly disabled
      if (oracleConfig.streamProcessing === false) {
        return await this.executeRegularQuery(context, oracleConfig, startTime);
      } else {
        return await this.executeStreamingQuery(
          context,
          oracleConfig,
          startTime
        );
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown Oracle error";
      await context.addLog(`Oracle operation failed: ${errorMessage}`);

      logger.error(
        `Oracle database operation failed for job ${context.jobId}`,
        {
          jobId: context.jobId,
          error: errorMessage,
          host: oracleConfig.host,
          port: oracleConfig.port,
          serviceName: oracleConfig.serviceName,
        }
      );

      throw new Error(`Oracle database operation failed: ${errorMessage}`);
    }
  }

  private async executeStreamingQuery(
    context: JobExecutionContext,
    oracleConfig: NonNullable<JobDefinition["dataSource"]["oracle"]>,
    startTime: number
  ): Promise<JobResult> {
    const batchSize = oracleConfig.batchSize || 10000;
    let totalRecords = 0;
    let batchCount = 0;
    const allRows: OracleRow[] = [];

    await context.addLog(
      `Starting streaming query with batch size: ${batchSize}`
    );

    await context.executeWithCancellationCheck(
      () =>
        executeOracleQueryStream(
          oracleConfig.query,
          [],
          {
            user: oracleConfig.username,
            password: oracleConfig.password,
            connectString: `${oracleConfig.host}:${oracleConfig.port}/${oracleConfig.serviceName}`,
            batchSize,
          },
          async (batch: OracleRow[]) => {
            await context.checkCancellation();

            batchCount++;
            totalRecords += batch.length;

            // Store batch data (consider writing to temp storage for very large datasets)
            allRows.push(...batch);

            await context.addLog(
              `Processed batch ${batchCount}: ${batch.length} records (Total: ${totalRecords})`
            );

            // Optional: Implement memory management for extremely large datasets
            if (allRows.length > 100000) {
              // 100k records threshold
              await context.addLog(
                "Warning: Large dataset detected. Consider implementing file-based storage."
              );
            }
          }
        ),
      "Oracle streaming query execution"
    );

    await context.addLog(
      `Streaming query completed. Total records: ${totalRecords}`
    );

    await this.logOperationComplete(
      context,
      "Oracle streaming operation",
      startTime,
      `${totalRecords} records retrieved in ${batchCount} batches`
    );

    logger.info(
      `Oracle streaming query executed successfully for job ${context.jobId}`,
      {
        jobId: context.jobId,
        totalRecords,
        batchCount,
        batchSize,
        query: oracleConfig.query.substring(0, 100) + "...",
      }
    );

    return this.createJobResult(totalRecords, allRows);
  }

  private async executeRegularQuery(
    context: JobExecutionContext,
    oracleConfig: NonNullable<JobDefinition["dataSource"]["oracle"]>,
    startTime: number
  ): Promise<JobResult> {
    await context.addLog("Executing SQL query...");

    const result = (await context.executeWithCancellationCheck(
      () =>
        executeOracleQuery(oracleConfig.query, [], {
          user: oracleConfig.username,
          password: oracleConfig.password,
          connectString: `${oracleConfig.host}:${oracleConfig.port}/${oracleConfig.serviceName}`,
          // Only apply maxRows if explicitly set by user, no default limit
          ...(oracleConfig.maxRows && { maxRows: oracleConfig.maxRows }),
        }),
      "Oracle query execution"
    )) as OracleQueryResult;

    const recordCount = result.rows ? result.rows.length : 0;

    await context.addLog(
      `Query executed successfully. Retrieved ${recordCount} records`
    );

    await this.logOperationComplete(
      context,
      "Oracle database operation",
      startTime,
      `${recordCount} records retrieved`
    );

    logger.info(`Oracle query executed successfully for job ${context.jobId}`, {
      jobId: context.jobId,
      recordCount,
      query: oracleConfig.query.substring(0, 100) + "...",
    });

    return this.createJobResult(recordCount, result.rows || []);
  }

  /**
   * Validate Oracle job configuration
   */
  public validateConfig(jobDef: JobDefinition): boolean {
    if (!super.validateConfig(jobDef)) {
      return false;
    }

    const oracleConfig = jobDef.dataSource.oracle;
    if (!oracleConfig) {
      return false;
    }

    try {
      this.validateOracleConfig(oracleConfig);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get required permissions for Oracle operations
   */
  public getRequiredPermissions(): string[] {
    return ["database:oracle:read"];
  }

  /**
   * Validate Oracle configuration fields
   */
  private validateOracleConfig(
    config: NonNullable<JobDefinition["dataSource"]["oracle"]>
  ): void {
    this.validateRequiredFields(
      config,
      ["host", "port", "serviceName", "username", "password", "query"],
      "Oracle configuration"
    );

    if (
      typeof config.port !== "number" ||
      config.port <= 0 ||
      config.port > 65535
    ) {
      throw new Error("Oracle port must be a valid number between 1 and 65535");
    }

    if (!config.query.trim()) {
      throw new Error("Oracle query cannot be empty");
    }

    // Validate batch size (streaming is default unless explicitly disabled)
    if (config.streamProcessing !== false && config.batchSize) {
      if (config.batchSize <= 0 || config.batchSize > 100000) {
        throw new Error("Batch size must be between 1 and 100000");
      }
    }

    // Basic SQL injection prevention
    const dangerousPatterns = [
      /;\s*drop\s+/i,
      /;\s*delete\s+/i,
      /;\s*truncate\s+/i,
      /;\s*alter\s+/i,
      /;\s*create\s+/i,
    ];

    for (const pattern of dangerousPatterns) {
      if (pattern.test(config.query)) {
        throw new Error(
          "Oracle query contains potentially dangerous SQL statements"
        );
      }
    }
  }
}
